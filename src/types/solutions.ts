export interface Solution {
  initial_thoughts: string[]
  thought_steps: string[]
  description: string
  code: string
}

export interface SolutionsResponse {
  [key: string]: Solution
}

export interface ProblemStatementData {
  problem_type: "MCQ" | "CODING" | "SQL";
  problem_statement: string;
  input_format?: {
    description: string;
    parameters: any[];
  };
  output_format?: {
    description: string;
    type: string;
    subtype: string;
  };
  complexity?: {
    time: string;
    space: string;
  };
  test_cases?: any[];
  validation_type?: string;
  difficulty?: string;
  choices?: string[];
  is_multi_select?: boolean;
  // SQL-specific fields
  database_schema?: Array<{
    table_name: string;
    columns: Array<{
      name: string;
      type: string;
      constraints?: string[];
    }>;
  }>;
  sql_dialect?: "PostgreSQL" | "MySQL" | "SQLite" | "T-SQL" | "Oracle";
  sample_data?: Array<{
    table_name: string;
    rows: any[];
  }>;
}